import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { EventsHeader } from "@/components/screens/events/events-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import { SearchInput } from "@/components/shared/search";
import { EventsList } from "@/components/screens/events/events-list";
import { useEvents } from "@/hooks/useEvents";

export const Events = () => {
  const {
    events,
    isLoading,
    isRefetching,
    selectedDate,
    searchTerm,
    handleDateChange,
    handleSearch,
    clearSearch,
    refetch,
    setSearchTerm,
  } = useEvents();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <EventsHeader />
        
        {/* Month Picker */}
        <HorizontalDatePicker
          selectedDate={selectedDate}
          onDateSelect={handleDateChange}
          config={{
            mode: "months",
            showCalendarButton: false,
            itemCount: 12,
            itemWidth: 80,
          }}
        />
        
        {/* Search */}
        <SearchInput 
          onSearch={setSearchTerm} 
          searchTerm={searchTerm}
          placeholder="Search events"
        />
        
        {/* Events List */}
        <VStack className="flex-1 bg-gray-100">
          <EventsList
            events={events}
            isLoading={isLoading}
            isRefreshing={isRefetching}
            searchTerm={searchTerm}
            onRefresh={refetch}
            onClearSearch={clearSearch}
          />
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Events;
