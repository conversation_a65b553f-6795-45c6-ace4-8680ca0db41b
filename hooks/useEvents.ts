import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useEventsQuery } from "@/data/screens/events/queries/useEventsQuery";
import { EventResponse } from "@/data/screens/events/types";
import { format } from "date-fns";

export const useEvents = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Format date for API
  const formattedDate = format(selectedDate, "yyyy-MM-dd");

  // Fetch events data
  const {
    data: eventsData = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useEventsQuery({
    month: format(selectedDate, "yyyy-MM"),
  });

  // Filter events based on search term and category
  const filteredData = useMemo(() => {
    let filtered = eventsData;

    // Filter by search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["title", "location", "instructor", "category"],
      });
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(event => event.category === selectedCategory);
    }

    return filtered;
  }, [eventsData, searchTerm, selectedCategory]);

  // Get unique categories for filter
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(eventsData.map(event => event.category))];
    return uniqueCategories.sort();
  }, [eventsData]);

  // Handle date change
  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  // Handle search
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // Handle category filter
  const handleCategoryFilter = useCallback((category: string | null) => {
    setSelectedCategory(category);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setSelectedCategory(null);
  }, []);

  return {
    // Data
    events: filteredData,
    categories,
    
    // Loading states
    isLoading,
    isRefetching,
    error,
    
    // UI State
    selectedDate,
    searchTerm,
    selectedCategory,
    
    // Actions
    handleDateChange,
    handleSearch,
    handleCategoryFilter,
    clearSearch,
    clearFilters,
    refetch,
    setSearchTerm,
  };
};
