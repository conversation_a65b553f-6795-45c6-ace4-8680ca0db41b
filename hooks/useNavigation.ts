import { useCallback } from "react";
import { router } from "expo-router";
import { Alert } from "react-native";
import { useUIStore } from "@/stores/ui-store";

export const useNavigation = () => {
  const { setActiveTab, showToast } = useUIStore();

  const navigateTo = useCallback(
    (path: string) => {
      try {
        router.push(path as any);
      } catch (error) {
        showToast("Navigation failed", "error");
        console.error("Navigation error:", error);
      }
    },
    [showToast]
  );

  const navigateReplace = useCallback(
    (path: string) => {
      try {
        router.replace(path as any);
      } catch (error) {
        showToast("Navigation failed", "error");
        console.error("Navigation error:", error);
      }
    },
    [showToast]
  );

  const goBack = useCallback(() => {
    try {
      router.back();
    } catch (error) {
      showToast("Navigation failed", "error");
      console.error("Navigation error:", error);
    }
  }, [showToast]);

  const navigateToAuth = useCallback(() => {
    navigateReplace("/(auth)/sign-in");
  }, [navigateReplace]);

  const navigateToHome = useCallback(() => {
    navigateReplace("/(tabs)/(home)");
  }, [navigateReplace]);

  const navigateToClasses = useCallback(() => {
    setActiveTab("classes");
    navigateTo("/(tabs)/classes");
  }, [navigateTo, setActiveTab]);

  const navigateToSettings = useCallback(() => {
    setActiveTab("settings");
    navigateTo("/(tabs)/settings");
  }, [navigateTo, setActiveTab]);

  const navigateToMore = useCallback(() => {
    setActiveTab("more");
    navigateTo("/(tabs)/more");
  }, [navigateTo, setActiveTab]);

  const navigateToSocial = useCallback(() => {
    setActiveTab("social");
    navigateTo("/(tabs)/social");
  }, [navigateTo, setActiveTab]);

  const navigateToTrainers = useCallback(() => {
    navigateTo("/trainers");
  }, [navigateTo]);

  const navigateToTrainerDetails = useCallback(
    (trainerId: string) => {
      navigateTo(`/trainer-details?id=${trainerId}`);
    },
    [navigateTo]
  );

  const navigateToClassDetails = useCallback(
    (classId: string) => {
      navigateTo(`/(class-details)/${classId}`);
    },
    [navigateTo]
  );

  const navigateToLocations = useCallback(() => {
    navigateTo("/locations");
  }, [navigateTo]);

  const navigateToOtherPrograms = useCallback(() => {
    navigateTo("/other-programs");
  }, [navigateTo]);

  const navigateToProgramDetails = useCallback(
    (programId: string) => {
      navigateTo(`/program-details?id=${programId}`);
    },
    [navigateTo]
  );

  const navigateToEvents = useCallback(() => {
    navigateTo("/events");
  }, [navigateTo]);

  const navigateToTerms = useCallback(() => {
    navigateTo("/terms");
  }, [navigateTo]);

  const navigateToForgotPassword = useCallback(() => {
    navigateTo("/(auth)/forgot-password");
  }, [navigateTo]);

  const navigateToResetPassword = useCallback(() => {
    navigateTo("/(auth)/reset-password");
  }, [navigateTo]);

  const confirmNavigation = useCallback(
    (
      title: string,
      message: string,
      destination: string,
      onConfirm?: () => void
    ) => {
      Alert.alert(title, message, [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Continue",
          onPress: () => {
            onConfirm?.();
            navigateTo(destination);
          },
        },
      ]);
    },
    [navigateTo]
  );

  const confirmSignOut = useCallback((onSignOut: () => void) => {
    Alert.alert("Sign Out", "Are you sure you want to sign out?", [
      {
        text: "Cancel",
        style: "cancel",
      },
      {
        text: "Sign Out",
        style: "destructive",
        onPress: onSignOut,
      },
    ]);
  }, []);

  return {
    // Basic navigation
    navigateTo,
    navigateReplace,
    goBack,

    // Specific routes
    navigateToAuth,
    navigateToHome,
    navigateToClasses,
    navigateToSettings,
    navigateToMore,
    navigateToSocial,
    navigateToTrainers,
    navigateToTrainerDetails,
    navigateToClassDetails,
    navigateToLocations,
    navigateToOtherPrograms,
    navigateToProgramDetails,
    navigateToEvents,
    navigateToTerms,
    navigateToForgotPassword,
    navigateToResetPassword,

    // Confirmation dialogs
    confirmNavigation,
    confirmSignOut,
  };
};
