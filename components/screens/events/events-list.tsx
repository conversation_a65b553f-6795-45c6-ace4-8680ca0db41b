import React from "react";
import { FlatList, RefreshControl } from "react-native";
import { EventCard } from "./event-card";
import { EventResponse } from "@/data/screens/events/types";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";

interface EventsListProps {
  events: EventResponse[];
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  onRefresh: () => void;
  onClearSearch: () => void;
}

const EmptyState = ({ searchTerm, onClearSearch }: { searchTerm: string; onClearSearch: () => void }) => {
  return (
    <VStack className="items-center justify-center py-16 px-4" space="md">
      <Text className="text-lg font-dm-sans-bold text-typography-900 text-center">
        {searchTerm ? "No events found" : "No events available"}
      </Text>
      <Text className="text-sm font-dm-sans-regular text-typography-600 text-center">
        {searchTerm 
          ? "Try adjusting your search or filters"
          : "Check back later for upcoming events"
        }
      </Text>
      {searchTerm && (
        <Text 
          onPress={onClearSearch}
          className="text-sm font-dm-sans-medium text-primary-500 text-center"
        >
          Clear search
        </Text>
      )}
    </VStack>
  );
};

const LoadingSkeleton = () => {
  return (
    <VStack space="md" className="px-4 py-4">
      {Array.from({ length: 5 }).map((_, index) => (
        <VStack key={index} className="bg-white rounded-2xl border border-background-100 p-4">
          <VStack className="animate-pulse" space="sm">
            <VStack className="h-4 bg-background-200 rounded w-3/4" />
            <VStack className="h-3 bg-background-200 rounded w-1/2" />
            <VStack className="h-3 bg-background-200 rounded w-2/3" />
          </VStack>
        </VStack>
      ))}
    </VStack>
  );
};

export const EventsList = ({
  events,
  isLoading,
  isRefreshing,
  searchTerm,
  onRefresh,
  onClearSearch,
}: EventsListProps) => {
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  if (events.length === 0) {
    return <EmptyState searchTerm={searchTerm} onClearSearch={onClearSearch} />;
  }

  return (
    <FlatList
      data={events}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <EventCard
          event={item}
          onPress={() => {
            // Handle event press - navigate to event details
            console.log("Event pressed:", item.id);
          }}
          onFavoritePress={() => {
            // Handle favorite press
            console.log("Favorite pressed:", item.id);
          }}
        />
      )}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingTop: 16,
        paddingBottom: 100,
      }}
    />
  );
};
