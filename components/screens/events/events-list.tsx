import React from "react";
import { <PERSON><PERSON>, FlatList, RefreshControl } from "react-native";
import { EventResponse } from "@/data/screens/events/types";

import {
  ActivityCard,
  ActivityCardData,
} from "@/components/shared/activity-card";
import { StatusButton } from "@/components/shared/status-button";
import { CardSkeletonLoader } from "../../shared/card-skeleton";
import { EmptySearchState } from "@/components/shared/empty-search";

interface EventsListProps {
  events: EventResponse[];
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  onRefresh: () => void;
  onClearSearch: () => void;
}

const convertEventToActivityCard = (
  event: EventResponse
): ActivityCardData => ({
  id: event.id,
  title: event.title,
  imageUrl: event.image_url,
  startTime: event.start_time,
  endTime: event.end_time,
  spotsLeft: event.spots_available,
  instructor: event.instructor,
  location: event.location,
  isFavorite: false, // TODO: implement favorites
});

const getStatusButtonConfig = (status: EventResponse["status"]) => {
  switch (status) {
    case "paid":
      return { variant: "reserve" as const, text: "Reserve", disabled: false };
    case "cancelled":
      return {
        variant: "cancel_reservation" as const,
        text: "Cancel reservation",
        disabled: false,
      };
    case "event_cancelled":
      return {
        variant: "event_cancelled" as const,
        text: "Event cancelled",
        disabled: true,
      };
    case "event_full":
      return {
        variant: "event_full" as const,
        text: "Event full",
        disabled: true,
      };
    case "join_waitlist":
      return {
        variant: "join_waitlist" as const,
        text: "Join waitlist",
        disabled: false,
      };
    default:
      return { variant: "reserve" as const, text: "Reserve", disabled: false };
  }
};

export const EventsList = ({
  events,
  isLoading,
  isRefreshing,
  searchTerm,
  onRefresh,
  onClearSearch,
}: EventsListProps) => {
  if (isLoading) {
    return <CardSkeletonLoader />;
  }

  if (events.length === 0 && searchTerm) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  return (
    <FlatList
      data={events}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => {
        const activityData = convertEventToActivityCard(item);
        const buttonConfig = getStatusButtonConfig(item.status);

        return (
          <ActivityCard
            data={activityData}
            onPress={() => {
              // Handle event press - navigate to event details
              Alert.alert("Event pressed:", item.id);
            }}
            showInstructor={false} // Events don't show instructor in the main view
            renderButton={() => (
              <StatusButton
                variant={buttonConfig.variant}
                text={buttonConfig.text}
                disabled={buttonConfig.disabled}
                size="sm"
                // onPress={() =>
                //   console.log("Button pressed:", item.id, buttonConfig.variant)
                // }
              />
            )}
          />
        );
      }}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingTop: 16,
        paddingBottom: 400,
      }}
    />
  );
};
