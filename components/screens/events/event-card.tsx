import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { MapPin, Heart, Calendar } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { EventResponse } from "@/data/screens/events/types";
import { Button, ButtonText } from "@/components/ui/button";

interface EventCardProps {
  event: EventResponse;
  onPress?: () => void;
  onFavoritePress?: () => void;
  isFavorite?: boolean;
}

const getStatusButton = (status: EventResponse["status"]) => {
  switch (status) {
    case "paid":
      return {
        text: "Reserve",
        variant: "solid" as const,
        className: "bg-primary-500",
        textClassName: "text-white",
      };
    case "cancelled":
      return {
        text: "Cancel reservation",
        variant: "outline" as const,
        className: "border-red-500",
        textClassName: "text-red-500",
      };
    case "event_cancelled":
      return {
        text: "Event cancelled",
        variant: "outline" as const,
        className: "border-gray-300",
        textClassName: "text-gray-500",
        disabled: true,
      };
    case "event_full":
      return {
        text: "Event full",
        variant: "outline" as const,
        className: "border-gray-300",
        textClassName: "text-gray-500",
        disabled: true,
      };
    case "join_waitlist":
      return {
        text: "Join waitlist",
        variant: "outline" as const,
        className: "border-primary-500",
        textClassName: "text-primary-500",
      };
    default:
      return {
        text: "Reserve",
        variant: "solid" as const,
        className: "bg-primary-500",
        textClassName: "text-white",
      };
  }
};

export const EventCard = ({
  event,
  onPress,
  onFavoritePress,
  isFavorite,
}: EventCardProps) => {
  const statusButton = getStatusButton(event.status);

  return (
    <Pressable onPress={onPress} className="mx-4 mb-3">
      <VStack className="bg-white rounded-2xl border border-background-100 overflow-hidden">
        <HStack className="p-4" space="md">
          {/* Event Icon */}
          <VStack className="items-center justify-center">
            <VStack className="w-12 h-12 bg-primary-100 rounded-lg items-center justify-center">
              <Icon as={Calendar} size="lg" className="text-primary-600" />
            </VStack>
          </VStack>

          {/* Event Details */}
          <VStack className="flex-1" space="xs">
            <HStack className="justify-between items-start">
              <VStack className="flex-1" space="xs">
                <HStack className="items-center" space="xs">
                  <Text className="text-base font-dm-sans-bold text-typography-900">
                    {event.title}
                  </Text>
                  <Text className="text-sm font-dm-sans-medium text-typography-600">
                    {event.status === "paid" ? "Paid" : ""}
                  </Text>
                </HStack>

                <Text className="text-sm font-dm-sans-regular text-typography-600">
                  {event.start_time} - {event.end_time} •{" "}
                  {event.spots_available} spots left
                </Text>

                <HStack className="items-center" space="xs">
                  <Icon as={MapPin} size="sm" className="text-typography-400" />
                  <Text className="text-sm font-dm-sans-regular text-typography-600">
                    {event.location}
                  </Text>
                </HStack>
              </VStack>

              {/* Favorite Button */}
              <Pressable onPress={onFavoritePress} className="p-1">
                <Icon
                  as={Heart}
                  size="md"
                  className={
                    isFavorite ? "text-red-500" : "text-typography-400"
                  }
                  fill={isFavorite ? "currentColor" : "none"}
                />
              </Pressable>
            </HStack>

            {/* Action Button */}
            <HStack className="justify-end mt-2">
              <Button
                variant={statusButton.variant}
                size="sm"
                className={`${statusButton.className} min-w-[120px]`}
                disabled={statusButton.disabled}
              >
                <ButtonText
                  className={`text-sm font-dm-sans-medium ${statusButton.textClassName}`}
                >
                  {statusButton.text}
                </ButtonText>
              </Button>
            </HStack>
          </VStack>
        </HStack>
      </VStack>
    </Pressable>
  );
};
