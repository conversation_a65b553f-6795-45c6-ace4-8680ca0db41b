import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { MapPin, Heart, Calendar } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { EventResponse } from "@/data/screens/events/types";
import {
  StatusButton,
  StatusButtonVariant,
} from "@/components/shared/status-button";

interface EventCardProps {
  event: EventResponse;
  onPress?: () => void;
  onFavoritePress?: () => void;
  isFavorite?: boolean;
}

const getStatusButtonConfig = (status: EventResponse["status"]) => {
  switch (status) {
    case "paid":
      return {
        text: "Reserve",
        variant: "reserve" as StatusButtonVariant,
        disabled: false,
      };
    case "cancelled":
      return {
        text: "Cancel reservation",
        variant: "cancel_reservation" as StatusButtonVariant,
        disabled: false,
      };
    case "event_cancelled":
      return {
        text: "Event cancelled",
        variant: "event_cancelled" as StatusButtonVariant,
        disabled: true,
      };
    case "event_full":
      return {
        text: "Event full",
        variant: "event_full" as StatusButtonVariant,
        disabled: true,
      };
    case "join_waitlist":
      return {
        text: "Join waitlist",
        variant: "join_waitlist" as StatusButtonVariant,
        disabled: false,
      };
    default:
      return {
        text: "Reserve",
        variant: "reserve" as StatusButtonVariant,
        disabled: false,
      };
  }
};

export const EventCard = ({
  event,
  onPress,
  onFavoritePress,
  isFavorite,
}: EventCardProps) => {
  const buttonConfig = getStatusButtonConfig(event.status);

  return (
    <Pressable onPress={onPress} className="mx-4 mb-3">
      <VStack className="bg-white rounded-2xl border border-background-100 overflow-hidden">
        <VStack className="p-4" space="sm">
          {/* Top Row: Image, Title, Time, Spots, Favorite */}
          <HStack className="items-start" space="md">
            {/* Event Icon */}
            <VStack className="w-12 h-12 bg-primary-100 rounded-lg items-center justify-center">
              <Icon as={Calendar} size="lg" className="text-primary-600" />
            </VStack>

            {/* Event Details */}
            <VStack className="flex-1" space="xs">
              <HStack className="justify-between items-start">
                <VStack className="flex-1" space="xs">
                  <HStack className="items-center" space="xs">
                    <Text className="text-sm font-dm-sans-bold text-[#00697B]">
                      {event.title}
                    </Text>
                    {event.status === "paid" && (
                      <VStack className="bg-[#E6F9FC] px-2 py-0.5 rounded">
                        <Text className="text-xs font-dm-sans-medium text-[#00697B]">
                          Paid
                        </Text>
                      </VStack>
                    )}
                  </HStack>

                  <Text className="text-xs font-dm-sans-regular text-typography-600">
                    {event.start_time} - {event.end_time} •{" "}
                    {event.spots_available} spots left
                  </Text>
                </VStack>

                {/* Favorite Button */}
                <Pressable onPress={onFavoritePress} className="p-1">
                  <Icon
                    as={Heart}
                    size="md"
                    className={
                      isFavorite ? "text-red-500" : "text-typography-400"
                    }
                    fill={isFavorite ? "currentColor" : "none"}
                  />
                </Pressable>
              </HStack>
            </VStack>
          </HStack>

          {/* Bottom Row: Location and Button */}
          <HStack className="justify-between items-center">
            <HStack className="items-center flex-1" space="xs">
              <Icon as={MapPin} size="sm" className="text-typography-400" />
              <Text className="text-xs font-dm-sans-regular text-typography-600">
                {event.location}
              </Text>
            </HStack>

            {/* Action Button */}
            <StatusButton
              variant={buttonConfig.variant}
              text={buttonConfig.text}
              disabled={buttonConfig.disabled}
              size="sm"
              onPress={() => {
                console.log("Button pressed:", event.id, buttonConfig.variant);
              }}
            />
          </HStack>
        </VStack>
      </VStack>
    </Pressable>
  );
};
