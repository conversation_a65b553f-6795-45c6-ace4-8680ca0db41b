import React, { useState, useRef, useEffect } from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { ScrollView } from "@/components/ui/scroll-view";
import { CalendarDays } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import CalendarWidget from "@/components/shared/calendar-widget";
import { View } from "react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
} from "@/components/ui/actionsheet";
import {
  format,
  isToday as isTodayFn,
  isSameDay,
  addDays,
  startOfDay,
  addMonths,
  startOfMonth,
  isSameMonth,
} from "date-fns";
import { Box } from "@/components/ui/box";

export type DatePickerMode = "days" | "months";

export interface DatePickerConfig {
  mode: DatePickerMode;
  showCalendarButton?: boolean;
  itemCount?: number;
  itemWidth?: number;
}

interface HorizontalDatePickerProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  config?: DatePickerConfig;
}

const generateDates = (count: number = 60) =>
  Array.from({ length: count }, (_, i) => addDays(startOfDay(new Date()), i));

const generateMonths = (count: number = 12) =>
  Array.from({ length: count }, (_, i) =>
    addMonths(startOfMonth(new Date()), i)
  );

const HorizontalDatePicker = ({
  selectedDate,
  onDateSelect,
  config = {
    mode: "days",
    showCalendarButton: true,
    itemCount: 60,
    itemWidth: 48,
  },
}: HorizontalDatePickerProps) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const { mode, showCalendarButton, itemCount, itemWidth = 48 } = config;

  const dates =
    mode === "days" ? generateDates(itemCount) : generateMonths(itemCount);

  const selectedIndex = dates.findIndex((date) =>
    mode === "days"
      ? isSameDay(date, selectedDate)
      : isSameMonth(date, selectedDate)
  );

  useEffect(() => {
    let clearTimeoutId: NodeJS.Timeout | null = null;

    if (selectedIndex !== -1 && scrollViewRef.current) {
      clearTimeoutId = setTimeout(() => {
        scrollViewRef.current?.scrollTo({
          x: selectedIndex * (itemWidth + 8), // item width + spacing
          animated: true,
        });
      }, 100);
    }

    return () => {
      if (clearTimeoutId) {
        clearTimeout(clearTimeoutId);
      }
    };
  }, [selectedDate, selectedIndex, itemWidth]);

  const formatDateItem = (date: Date) => {
    if (mode === "days") {
      const dayName = format(date, "EEEEEE").toUpperCase();
      const dayNumber = format(date, "d");
      return { primary: dayName, secondary: dayNumber };
    } else {
      const monthName = format(date, "MMM").toUpperCase();
      return { primary: monthName, secondary: "" };
    }
  };

  const isSelected = (date: Date) => {
    return mode === "days"
      ? isSameDay(date, selectedDate)
      : isSameMonth(date, selectedDate);
  };

  const handleDatePress = (date: Date) => {
    onDateSelect(date);
  };

  const handleCalendarSelect = (date: Date) => {
    onDateSelect(date);
    setShowCalendar(false);
  };

  return (
    <>
      <HStack className="items-center px-4 py-3" space="md">
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          className="flex-1"
          contentContainerStyle={{ paddingHorizontal: 8 }}
        >
          <HStack space="sm">
            {dates.map((date, index) => {
              const { primary, secondary } = formatDateItem(date);
              const selected = isSelected(date);
              const today = mode === "days" ? isTodayFn(date) : false;

              return (
                <Pressable
                  key={index}
                  onPress={() => handleDatePress(date)}
                  className="items-center"
                >
                  <VStack
                    className={`items-center text-xs justify-center px-3 py-3 ${
                      mode === "days"
                        ? "max-w-[48px] max-h-[70px]"
                        : "min-w-[80px] max-h-[70px]"
                    } ${
                      selected
                        ? "bg-[#00BFE0] rounded-full"
                        : "bg-transparent border border-background-200 rounded-full"
                    }`}
                    space="xs"
                  >
                    {mode === "days" ? (
                      <>
                        <Text
                          size="xs"
                          className={`text-xs ${
                            selected ? "text-black" : today ? "" : ""
                          }`}
                        >
                          {primary}
                        </Text>
                        <View className="flex-1 items-center justify-center gap-1">
                          <Text
                            size="xs"
                            className={`text-xs font-dm-sans-bold ${
                              selected ? "text-black" : "text-typography-900"
                            }`}
                          >
                            {secondary}
                          </Text>
                          {selected && (
                            <Box className="w-1 h-1 bg-black rounded-full" />
                          )}
                        </View>
                      </>
                    ) : (
                      <View className="items-center justify-center">
                        <Text
                          size="sm"
                          className={`font-dm-sans-bold ${
                            selected ? "text-black" : "text-typography-900"
                          }`}
                        >
                          {primary}
                        </Text>
                        {selected && (
                          <Box className="w-1 h-1 bg-black rounded-full mt-1" />
                        )}
                      </View>
                    )}
                  </VStack>
                </Pressable>
              );
            })}
          </HStack>
        </ScrollView>

        {showCalendarButton && (
          <Pressable
            onPress={() => setShowCalendar(true)}
            className="p-3 bg-white border border-background-200 rounded-full"
          >
            <Icon as={CalendarDays} size="lg" color="black" />
          </Pressable>
        )}
      </HStack>

      {showCalendarButton && (
        <Actionsheet
          isOpen={showCalendar}
          onClose={() => setShowCalendar(false)}
        >
          <ActionsheetBackdrop />
          <ActionsheetContent>
            <VStack className="w-full">
              <CalendarWidget
                selectedDate={selectedDate}
                onDateSelect={handleCalendarSelect}
              />
            </VStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </>
  );
};

export default HorizontalDatePicker;
