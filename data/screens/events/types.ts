export type EventStatus = "paid" | "cancelled" | "event_cancelled" | "event_full" | "join_waitlist";

export interface EventResponse {
  id: string;
  title: string;
  start_time: string;
  end_time: string;
  date: string;
  location: string;
  spots_available: number;
  total_spots: number;
  status: EventStatus;
  price?: number;
  description?: string;
  instructor?: string;
  image_url?: string;
  category: string;
}

export interface EventsQueryParams {
  date?: string;
  month?: string;
  category?: string;
}
