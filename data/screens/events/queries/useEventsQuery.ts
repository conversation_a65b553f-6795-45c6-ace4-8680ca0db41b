import { useQuery } from "@tanstack/react-query";
import { EventResponse, EventsQueryParams } from "../types";

const generateMockEvents = (): EventResponse[] => [
  {
    id: "1",
    title: "Rubin Run",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "<PERSON> & catz family",
    spots_available: 8,
    total_spots: 8,
    status: "paid",
    category: "Running",
    instructor: "<PERSON>e",
  },
  {
    id: "2",
    title: "Yoga Bliss",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "<PERSON> & catz family",
    spots_available: 8,
    total_spots: 8,
    status: "paid",
    category: "Yoga",
    instructor: "<PERSON>",
  },
  {
    id: "3",
    title: "Yoga Bliss",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "<PERSON> & cat<PERSON> family",
    spots_available: 8,
    total_spots: 8,
    status: "cancelled",
    category: "Yoga",
    instructor: "<PERSON>",
  },
  {
    id: "4",
    title: "Rubin Run",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "<PERSON> & catz family",
    spots_available: 0,
    total_spots: 8,
    status: "event_cancelled",
    category: "Running",
    instructor: "John Doe",
  },
  {
    id: "5",
    title: "Rubin Run",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "Collins & catz family",
    spots_available: 8,
    total_spots: 8,
    status: "paid",
    category: "Running",
    instructor: "John Doe",
  },
  {
    id: "6",
    title: "Rubin Run",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "Collins & catz family",
    spots_available: 8,
    total_spots: 8,
    status: "join_waitlist",
    category: "Running",
    instructor: "John Doe",
  },
  {
    id: "7",
    title: "Rubin Run",
    start_time: "5:30PM",
    end_time: "6:30PM",
    date: "2024-03-15",
    location: "Collins & catz family",
    spots_available: 8,
    total_spots: 8,
    status: "event_full",
    category: "Running",
    instructor: "John Doe",
  },
];

export const useEventsQuery = (params?: EventsQueryParams) => {
  return useQuery({
    queryKey: ["events", params],
    queryFn: async () => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      return generateMockEvents();
    },
  });
};
